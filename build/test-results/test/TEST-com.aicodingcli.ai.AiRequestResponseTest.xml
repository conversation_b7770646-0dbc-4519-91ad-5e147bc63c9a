<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.AiRequestResponseTest" tests="5" skipped="0" failures="0" errors="0" timestamp="2025-06-17T08:20:50.643Z" hostname="zxnapdeMacBook-Pro.local" time="0.065">
  <properties/>
  <testcase name="should create AI response with content()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.0"/>
  <testcase name="should create AI request with required fields()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.001"/>
  <testcase name="should handle streaming response()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.063"/>
  <testcase name="should handle conversation with multiple messages()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.0"/>
  <testcase name="should validate message content is not empty()" classname="com.aicodingcli.ai.AiRequestResponseTest" time="0.0"/>
  <system-out><![CDATA[16:20:50.645 [Test worker @kotlinx.coroutines.test runner#30] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiService name=#13
16:20:50.706 [Test worker @kotlinx.coroutines.test runner#30] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for Flow name=child of #13#14
16:20:50.707 [Test worker @kotlinx.coroutines.test runner#30] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering kotlinx.coroutines.flow.FlowKt__BuildersKt$flowOf$$inlined$unsafeFlow$1@3c770db4 on AiService(#13).streamChat(AiRequest(messages=[AiMessage(role=USER, content=Hello)], model=gpt-3.5-turbo, temperature=0.7, maxTokens=1000, stream=false), continuation {})
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
