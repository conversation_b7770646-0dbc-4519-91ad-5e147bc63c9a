<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.http.HttpClientTest" tests="8" skipped="0" failures="0" errors="0" timestamp="2025-06-17T08:20:50.756Z" hostname="zxnapdeMacBook-Pro.local" time="0.487">
  <properties/>
  <testcase name="should fail after max retries exceeded()" classname="com.aicodingcli.http.HttpClientTest" time="0.021"/>
  <testcase name="should make successful POST request with JSON body()" classname="com.aicodingcli.http.HttpClientTest" time="0.019"/>
  <testcase name="should add custom headers()" classname="com.aicodingcli.http.HttpClientTest" time="0.002"/>
  <testcase name="should make successful GET request()" classname="com.aicodingcli.http.HttpClientTest" time="0.002"/>
  <testcase name="should handle timeout()" classname="com.aicodingcli.http.HttpClientTest" time="0.422"/>
  <testcase name="should retry on network errors()" classname="com.aicodingcli.http.HttpClientTest" time="0.009"/>
  <testcase name="should handle HTTP error responses()" classname="com.aicodingcli.http.HttpClientTest" time="0.004"/>
  <testcase name="should handle rate limiting with retry after()" classname="com.aicodingcli.http.HttpClientTest" time="0.005"/>
  <system-out><![CDATA[16:20:50.769 [Test worker @kotlinx.coroutines.test runner#58] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:50.772 [Test worker @kotlinx.coroutines.test runner#58] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
16:20:50.774 [Test worker @kotlinx.coroutines.test runner#58] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:50.775 [Test worker @kotlinx.coroutines.test runner#58] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
16:20:50.776 [Test worker @kotlinx.coroutines.test runner#58] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:50.776 [Test worker @kotlinx.coroutines.test runner#58] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Persistent network error
16:20:50.780 [Test worker @kotlinx.coroutines.test runner#66] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/create
METHOD: HttpMethod(value=POST)
16:20:50.789 [Test worker @kotlinx.coroutines.test runner#66] INFO io.ktor.client.HttpClient -- RESPONSE: 201 Created
METHOD: HttpMethod(value=POST)
FROM: https://api.example.com/create
16:20:50.797 [Test worker @kotlinx.coroutines.test runner#70] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:50.798 [Test worker @kotlinx.coroutines.test runner#70] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
16:20:50.800 [Test worker @kotlinx.coroutines.test runner#74] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:50.800 [Test worker @kotlinx.coroutines.test runner#74] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
16:20:50.801 [Test worker @kotlinx.coroutines.test runner#78] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
16:20:50.906 [Test worker @kotlinx.coroutines.test runner#78] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
16:20:50.907 [Test worker @kotlinx.coroutines.test runner#78] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
16:20:51.013 [Test worker @kotlinx.coroutines.test runner#78] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
16:20:51.014 [Test worker @kotlinx.coroutines.test runner#78] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
16:20:51.117 [Test worker @kotlinx.coroutines.test runner#78] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
16:20:51.119 [Test worker @kotlinx.coroutines.test runner#78] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/slow
METHOD: HttpMethod(value=GET)
16:20:51.220 [Test worker @kotlinx.coroutines.test runner#78] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/slow failed with exception: java.util.concurrent.CancellationException: Request timeout has expired [url=https://api.example.com/slow, request_timeout=100 ms]
16:20:51.226 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:51.227 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
16:20:51.229 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:51.230 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST https://api.example.com/test failed with exception: java.lang.Exception: Network error
16:20:51.231 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:51.231 [Test worker @kotlinx.coroutines.test runner#88] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
16:20:51.235 [Test worker @kotlinx.coroutines.test runner#96] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/notfound
METHOD: HttpMethod(value=GET)
16:20:51.236 [Test worker @kotlinx.coroutines.test runner#96] INFO io.ktor.client.HttpClient -- RESPONSE: 404 Not Found
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/notfound
16:20:51.240 [Test worker @kotlinx.coroutines.test runner#100] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:51.240 [Test worker @kotlinx.coroutines.test runner#100] INFO io.ktor.client.HttpClient -- RESPONSE: 429 Too Many Requests
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
16:20:51.241 [Test worker @kotlinx.coroutines.test runner#100] INFO io.ktor.client.HttpClient -- REQUEST: https://api.example.com/test
METHOD: HttpMethod(value=GET)
16:20:51.242 [Test worker @kotlinx.coroutines.test runner#100] INFO io.ktor.client.HttpClient -- RESPONSE: 200 OK
METHOD: HttpMethod(value=GET)
FROM: https://api.example.com/test
]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
