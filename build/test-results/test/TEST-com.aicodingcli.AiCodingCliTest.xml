<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.AiCodingCliTest" tests="2" skipped="0" failures="0" errors="0" timestamp="2025-06-17T07:28:17.124Z" hostname="zxnapdeMacBook-Pro.local" time="0.01">
  <properties/>
  <testcase name="should print help when --help argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.008"/>
  <testcase name="should print version when --version argument is provided()" classname="com.aicodingcli.AiCodingCliTest" time="0.0"/>
  <system-out><![CDATA[]]></system-out>
  <system-err><![CDATA[]]></system-err>
</testsuite>
