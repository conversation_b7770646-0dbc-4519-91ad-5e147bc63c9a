<?xml version="1.0" encoding="UTF-8"?>
<testsuite name="com.aicodingcli.ai.providers.OpenAiServiceTest" tests="7" skipped="0" failures="0" errors="0" timestamp="2025-06-17T08:20:50.040Z" hostname="zxnapdeMacBook-Pro.local" time="0.6">
  <properties/>
  <testcase name="should use custom base URL if provided()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.577"/>
  <testcase name="should test connection successfully()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.005"/>
  <testcase name="should fail connection test on error()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.003"/>
  <testcase name="should handle streaming chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.002"/>
  <testcase name="should validate request before sending()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.001"/>
  <testcase name="should make successful chat request()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.004"/>
  <testcase name="should handle API error response()" classname="com.aicodingcli.ai.providers.OpenAiServiceTest" time="0.004"/>
  <system-out><![CDATA[16:20:50.322 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#1
16:20:50.479 [Test worker] WARN io.mockk.proxy.jvm.transformation.InliningClassTransformer -- Failed to transform class java/lang/Object
java.lang.IllegalArgumentException: Java 23 (67) is not supported by the current version of Byte Buddy which officially supports Java 21 (65) - update Byte Buddy or set net.bytebuddy.experimental as a VM property
	at net.bytebuddy.utility.OpenedClassReader.of(OpenedClassReader.java:96)
	at net.bytebuddy.dynamic.scaffold.TypeWriter$Default$ForInlining.create(TypeWriter.java:4011)
	at net.bytebuddy.dynamic.scaffold.TypeWriter$Default.make(TypeWriter.java:2224)
	at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase$UsingTypeWriter.make(DynamicType.java:4055)
	at net.bytebuddy.dynamic.DynamicType$Builder$AbstractBase.make(DynamicType.java:3739)
	at io.mockk.proxy.jvm.transformation.InliningClassTransformer.transform(InliningClassTransformer.kt:81)
	at java.instrument/java.lang.instrument.ClassFileTransformer.transform(ClassFileTransformer.java:242)
	at java.instrument/sun.instrument.TransformerManager.transform(TransformerManager.java:188)
	at java.instrument/sun.instrument.InstrumentationImpl.transform(InstrumentationImpl.java:610)
	at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses0(Native Method)
	at java.instrument/sun.instrument.InstrumentationImpl.retransformClasses(InstrumentationImpl.java:225)
	at io.mockk.proxy.jvm.transformation.JvmInlineInstrumentation.retransform(JvmInlineInstrumentation.kt:28)
	at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(RetransformInlineInstrumentation.kt:19)
	at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation$execute$1.invoke(RetransformInlineInstrumentation.kt:16)
	at io.mockk.proxy.common.transformation.ClassTransformationSpecMap.applyTransformation(ClassTransformationSpecMap.kt:41)
	at io.mockk.proxy.common.transformation.RetransformInlineInstrumentation.execute(RetransformInlineInstrumentation.kt:16)
	at io.mockk.proxy.jvm.ProxyMaker.inline(ProxyMaker.kt:90)
	at io.mockk.proxy.jvm.ProxyMaker.proxy(ProxyMaker.kt:34)
	at io.mockk.impl.instantiation.JvmMockFactory.newProxy(JvmMockFactory.kt:34)
	at io.mockk.impl.instantiation.AbstractMockFactory.newProxy$default(AbstractMockFactory.kt:24)
	at io.mockk.impl.instantiation.AbstractMockFactory.mockk(AbstractMockFactory.kt:59)
	at com.aicodingcli.ai.providers.OpenAiServiceTest.setUp(OpenAiServiceTest.kt:280)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptLifecycleMethod(TimeoutExtension.java:128)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptBeforeEachMethod(TimeoutExtension.java:78)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeMethodInExtensionContext(ClassBasedTestDescriptor.java:521)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$synthesizeBeforeEachMethodAdapter$23(ClassBasedTestDescriptor.java:506)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeBeforeEachMethods$3(TestMethodTestDescriptor.java:175)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeBeforeMethodsOrCallbacksUntilExceptionOccurs$6(TestMethodTestDescriptor.java:203)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeBeforeMethodsOrCallbacksUntilExceptionOccurs(TestMethodTestDescriptor.java:203)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeBeforeEachMethods(TestMethodTestDescriptor.java:172)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:135)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1597)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:107)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:88)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:67)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:52)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
	at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.processAllTestClasses(JUnitPlatformTestClassProcessor.java:124)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor$CollectAllTestClassesExecutor.access$000(JUnitPlatformTestClassProcessor.java:99)
	at org.gradle.api.internal.tasks.testing.junitplatform.JUnitPlatformTestClassProcessor.stop(JUnitPlatformTestClassProcessor.java:94)
	at org.gradle.api.internal.tasks.testing.SuiteTestClassProcessor.stop(SuiteTestClassProcessor.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:36)
	at org.gradle.internal.dispatch.ReflectionDispatch.dispatch(ReflectionDispatch.java:24)
	at org.gradle.internal.dispatch.ContextClassLoaderDispatch.dispatch(ContextClassLoaderDispatch.java:33)
	at org.gradle.internal.dispatch.ProxyDispatchAdapter$DispatchingInvocationHandler.invoke(ProxyDispatchAdapter.java:92)
	at jdk.proxy1/jdk.proxy1.$Proxy4.stop(Unknown Source)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker$3.run(TestWorker.java:200)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.executeAndMaintainThreadName(TestWorker.java:132)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:103)
	at org.gradle.api.internal.tasks.testing.worker.TestWorker.execute(TestWorker.java:63)
	at org.gradle.process.internal.worker.child.ActionExecutionWorker.execute(ActionExecutionWorker.java:56)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:122)
	at org.gradle.process.internal.worker.child.SystemApplicationClassLoaderWorker.call(SystemApplicationClassLoaderWorker.java:72)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.run(GradleWorkerMain.java:69)
	at worker.org.gradle.process.internal.worker.GradleWorkerMain.main(GradleWorkerMain.java:74)
16:20:50.596 [Test worker @kotlinx.coroutines.test runner#2] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #1#2
16:20:50.603 [Test worker @kotlinx.coroutines.test runner#2] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-test",
    "object": "chat.completion",
    "created": 1677652288,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hi"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 1,
        "completion_tokens": 1,
        "total_tokens": 2
    }
}, headers={}) on <EMAIL>(https://custom.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
16:20:50.619 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#3
16:20:50.621 [Test worker @kotlinx.coroutines.test runner#8] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #3#4
16:20:50.622 [Test worker @kotlinx.coroutines.test runner#8] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={"object": "list", "data": [{"id": "gpt-3.5-turbo", "object": "model", "created": 1677610602, "owned_by": "openai"}]}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
16:20:50.625 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#5
16:20:50.626 [Test worker @kotlinx.coroutines.test runner#12] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #5#6
16:20:50.627 [Test worker @kotlinx.coroutines.test runner#12] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/models, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
16:20:50.629 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#7
16:20:50.631 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#8
16:20:50.633 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#9
16:20:50.634 [Test worker @kotlinx.coroutines.test runner#20] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #9#10
16:20:50.634 [Test worker @kotlinx.coroutines.test runner#20] DEBUG io.mockk.impl.recording.states.AnsweringState -- Answering HttpResponse(status=200 OK, body={
    "id": "chatcmpl-123",
    "object": "chat.completion",
    "created": 1677652288,
    "model": "gpt-3.5-turbo",
    "choices": [
        {
            "index": 0,
            "message": {
                "role": "assistant",
                "content": "Hello! How can I help you today?"
            },
            "finish_reason": "stop"
        }
    ],
    "usage": {
        "prompt_tokens": 9,
        "completion_tokens": 12,
        "total_tokens": 21
    }
}, headers={Content-Type=application/json}) on <EMAIL>(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello, AI!"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
16:20:50.637 [Test worker] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for AiHttpClient name=#11
16:20:50.638 [Test worker @kotlinx.coroutines.test runner#26] DEBUG io.mockk.impl.instantiation.AbstractMockFactory -- Creating mockk for HttpResponse name=child of #11#12
16:20:50.638 [Test worker @kotlinx.coroutines.test runner#26] DEBUG io.mockk.impl.recording.states.AnsweringState -- Throwing com.aicodingcli.http.HttpException: HTTP error 401: <NAME_EMAIL>(https://api.openai.com/v1/chat/completions, {"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"temperature":0.7,"max_tokens":1000}, {Authorization=Bearer test-api-key, Content-Type=application/json}, continuation {})
]]></system-out>
  <system-err><![CDATA[WARNING: A Java agent has been loaded dynamically (/Users/<USER>/.gradle/caches/modules-2/files-2.1/net.bytebuddy/byte-buddy-agent/1.14.6/46e2545d7a97b6ccb195621650c5957279eb4812/byte-buddy-agent-1.14.6.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
</testsuite>
